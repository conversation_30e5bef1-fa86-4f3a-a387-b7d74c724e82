Not searching for unused variables given on the command line.
-- The C compiler identification is Clang 17.0.2
-- The CXX compiler identification is Clang 17.0.2
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Configuring done
-- Generating done
-- Build files have been written to: A:/Ascassy Project/Applications/GroMart-App-5.1/gromart_vendor/android/app/.cxx/Debug/6o3e3r11/x86_64
