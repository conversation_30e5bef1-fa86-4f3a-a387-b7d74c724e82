{"buildFiles": ["C:\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "A:\\Ascassy Project\\Applications\\GroMart-App-5.1\\gromart_vendor\\android\\app\\.cxx\\Debug\\6o3e3r11\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "A:\\Ascassy Project\\Applications\\GroMart-App-5.1\\gromart_vendor\\android\\app\\.cxx\\Debug\\6o3e3r11\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}