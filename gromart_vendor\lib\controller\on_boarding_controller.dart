import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:store/models/on_boarding_model.dart';
import 'package:store/utils/fire_store_utils.dart';

class OnBoardingController extends GetxController {
  var selectedPageIndex = 0.obs;

  bool get isLastPage => selectedPageIndex.value == onBoardingList.length - 1;
  var pageController = PageController();

  @override
  void onInit() {
    getOnBoardingData();
    super.onInit();
  }

  RxBool isLoading = true.obs;
  RxList<OnBoardingModel> onBoardingList = <OnBoardingModel>[].obs;

  getOnBoardingData() async {
    try {
      await FireStoreUtils.getOnBoardingList().then((value) {
        if (value.isNotEmpty) {
          onBoardingList.value = value;
        } else {
          // Fallback to default medicine-related onboarding data if Firestore returns empty
          onBoardingList.addAll([
            OnBoardingModel(
              id: "1",
              title: "Welcome to MedyPlus Store",
              description: "Manage your pharmacy inventory, prescriptions, and medicine orders efficiently in one place.",
              image: "assets/images/image_1.png"
            ),
            OnBoardingModel(
              id: "2",
              title: "Streamline Medicine Management",
              description: "Track medicine stock, handle prescription orders, and update your inventory in real-time with ease.",
              image: "assets/images/image_2.png"
            ),
            OnBoardingModel(
              id: "3",
              title: "Serve Your Patients Better",
              description: "Provide quick medicine delivery, manage prescriptions, and offer personalized healthcare services.",
              image: "assets/images/image_3.png"
            ),
          ]);
        }
      });
    } catch (e) {
      // Fallback to default medicine-related onboarding data if there's an error
      onBoardingList.addAll([
        OnBoardingModel(
          id: "1",
          title: "Welcome to MedyPlus Store",
          description: "Manage your pharmacy inventory, prescriptions, and medicine orders efficiently in one place.",
          image: "assets/images/image_1.png"
        ),
        OnBoardingModel(
          id: "2",
          title: "Streamline Medicine Management",
          description: "Track medicine stock, handle prescription orders, and update your inventory in real-time with ease.",
          image: "assets/images/image_2.png"
        ),
        OnBoardingModel(
          id: "3",
          title: "Serve Your Patients Better",
          description: "Provide quick medicine delivery, manage prescriptions, and offer personalized healthcare services.",
          image: "assets/images/image_3.png"
        ),
      ]);
    }

    isLoading.value = false;
    update();
  }
}
